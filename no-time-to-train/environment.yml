name: no-time-to-train
channels:
  - nvidia
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - _sysroot_linux-64_curr_repodata_hack=3=haa98f57_10
  - binutils_impl_linux-64=2.38=h2a08ee3_1
  - binutils_linux-64=2.38.0=hc2dff05_0
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2024.11.26=h06a4308_0
  - cuda=12.6.3=0
  - cuda-cccl_linux-64=12.6.77=0
  - cuda-command-line-tools=12.6.3=0
  - cuda-compiler=12.6.3=0
  - cuda-crt-dev_linux-64=12.6.85=0
  - cuda-crt-tools=12.6.85=0
  - cuda-cudart=12.6.77=0
  - cuda-cudart-dev=12.6.77=0
  - cuda-cudart-dev_linux-64=12.6.77=0
  - cuda-cudart-static=12.6.77=0
  - cuda-cudart-static_linux-64=12.6.77=0
  - cuda-cudart_linux-64=12.6.77=0
  - cuda-cuobjdump=12.6.77=0
  - cuda-cupti=12.6.80=0
  - cuda-cupti-dev=12.6.80=0
  - cuda-cuxxfilt=12.6.77=0
  - cuda-driver-dev=12.6.77=0
  - cuda-driver-dev_linux-64=12.6.77=0
  - cuda-gdb=12.6.77=0
  - cuda-libraries=12.6.3=0
  - cuda-libraries-dev=12.6.3=0
  - cuda-nsight=12.6.77=0
  - cuda-nvcc=12.6.85=0
  - cuda-nvcc-dev_linux-64=12.6.85=0
  - cuda-nvcc-impl=12.6.85=0
  - cuda-nvcc-tools=12.6.85=0
  - cuda-nvcc_linux-64=12.6.85=0
  - cuda-nvdisasm=12.6.77=0
  - cuda-nvml-dev=12.6.77=2
  - cuda-nvprof=12.6.80=0
  - cuda-nvprune=12.6.77=0
  - cuda-nvrtc=12.6.85=0
  - cuda-nvrtc-dev=12.6.85=0
  - cuda-nvtx=12.6.77=0
  - cuda-nvvm-dev_linux-64=12.6.85=0
  - cuda-nvvm-impl=12.6.85=0
  - cuda-nvvm-tools=12.6.85=0
  - cuda-nvvp=12.6.80=0
  - cuda-opencl=12.6.77=0
  - cuda-opencl-dev=12.6.77=0
  - cuda-profiler-api=12.6.77=0
  - cuda-runtime=12.6.3=0
  - cuda-sanitizer-api=12.6.77=0
  - cuda-toolkit=12.6.3=0
  - cuda-tools=12.6.3=0
  - cuda-version=12.6=3
  - cuda-visual-tools=12.6.3=0
  - dbus=1.13.18=hb2f20db_0
  - expat=2.6.4=h6a678d5_0
  - fontconfig=2.14.1=h55d465d_3
  - freetype=2.12.1=h4a9f257_0
  - gcc_impl_linux-64=11.2.0=h1234567_1
  - gcc_linux-64=11.2.0=h5c386dc_0
  - gds-tools=1.11.1.6=0
  - glib=2.78.4=h6a678d5_0
  - glib-tools=2.78.4=h6a678d5_0
  - gmp=6.2.1=h295c915_3
  - gxx_impl_linux-64=11.2.0=h1234567_1
  - gxx_linux-64=11.2.0=hc2dff05_0
  - icu=73.1=h6a678d5_0
  - kernel-headers_linux-64=3.10.0=h57e8cba_10
  - ld_impl_linux-64=2.38=h1181459_1
  - libcublas=12.6.4.1=0
  - libcublas-dev=12.6.4.1=0
  - libcufft=11.3.0.4=0
  - libcufft-dev=11.3.0.4=0
  - libcufile=1.11.1.6=0
  - libcufile-dev=1.11.1.6=0
  - libcurand=10.3.7.77=0
  - libcurand-dev=10.3.7.77=0
  - libcusolver=11.7.1.2=0
  - libcusolver-dev=11.7.1.2=0
  - libcusparse=12.5.4.2=0
  - libcusparse-dev=12.5.4.2=0
  - libffi=3.4.4=h6a678d5_1
  - libgcc-devel_linux-64=11.2.0=h1234567_1
  - libgcc-ng=11.2.0=h1234567_1
  - libglib=2.78.4=hdc74915_0
  - libgomp=11.2.0=h1234567_1
  - libiconv=1.16=h5eee18b_3
  - libnpp=12.2.5.30=0
  - libnpp-dev=12.2.5.30=0
  - libnvfatbin=12.6.77=0
  - libnvfatbin-dev=12.6.77=0
  - libnvjitlink=12.6.85=0
  - libnvjitlink-dev=12.6.85=0
  - libnvjpeg=**********=0
  - libnvjpeg-dev=**********=0
  - libpng=1.6.39=h5eee18b_0
  - libstdcxx-devel_linux-64=11.2.0=h1234567_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - libxcb=1.15=h7f8727e_0
  - libxkbcommon=1.0.1=h097e994_2
  - libxml2=2.13.5=hfdd30dd_0
  - ncurses=6.4=h6a678d5_0
  - nsight-compute=2024.3.2.3=0
  - nspr=4.35=h6a678d5_0
  - nss=3.89.1=h6a678d5_0
  - openssl=3.0.15=h5eee18b_0
  - pcre2=10.42=hebb0a14_1
  - pip=24.2=py311h06a4308_0
  - python=3.11.9=h955ad1f_0
  - readline=8.2=h5eee18b_0
  - setuptools=75.1.0=py311h06a4308_0
  - sqlite=3.45.3=h5eee18b_0
  - sysroot_linux-64=2.17=h57e8cba_10
  - tk=8.6.14=h39e8969_0
  - wheel=0.44.0=py311h06a4308_0
  - xz=5.4.6=h5eee18b_1
  - zlib=1.2.13=h5eee18b_1
  - pip:
      - addict==2.4.0
      - aiohappyeyeballs==2.4.3
      - aiohttp==3.10.10
      - aiosignal==1.3.1
      - antlr4-python3-runtime==4.9.3
      - anyio==4.5.0
      - appdirs==1.4.4
      - argon2-cffi==23.1.0
      - argon2-cffi-bindings==21.2.0
      - arrow==1.3.0
      - asttokens==2.4.1
      - async-lru==2.0.4
      - attrs==24.2.0
      - babel==2.16.0
      - beautifulsoup4==4.12.3
      - bleach==6.1.0
      - bpytop==1.0.68
      - cachetools==5.5.0
      - certifi==2024.8.30
      - cffi==1.17.1
      - charset-normalizer==3.3.2
      - click==8.1.7
      - cloudpickle==3.1.0
      - comm==0.2.2
      - contourpy==1.3.0
      - cycler==0.12.1
      - cython==3.0.11
      - debugpy==1.8.5
      - decorator==5.1.1
      - defusedxml==0.7.1
      - docker-pycreds==0.4.0
      - docstring-parser==0.16
      - executing==2.1.0
      - fastjsonschema==2.20.0
      - filelock==3.16.1
      - fonttools==4.53.1
      - fqdn==1.5.1
      - frozenlist==1.4.1
      - fsspec==2024.9.0
      - fvcore==0.1.5.post20221221
      - gitdb==4.0.11
      - gitpython==3.1.43
      - h11==0.14.0
      - httpcore==1.0.5
      - httpx==0.27.2
      - hydra-core==1.3.2
      - idna==3.10
      - imageio==2.35.1
      - importlib-metadata==8.5.0
      - importlib-resources==6.4.5
      - iopath==0.1.10
      - ipykernel==6.29.5
      - ipython==8.27.0
      - ipywidgets==8.1.5
      - isoduration==20.11.0
      - jedi==0.19.1
      - jinja2==3.1.4
      - joblib==1.4.2
      - json5==0.9.25
      - jsonargparse==4.33.2
      - jsonpointer==3.0.0
      - jsonschema==4.23.0
      - jsonschema-specifications==2023.12.1
      - jupyter==1.1.1
      - jupyter-client==8.6.3
      - jupyter-console==6.6.3
      - jupyter-core==5.7.2
      - jupyter-events==0.10.0
      - jupyter-lsp==2.2.5
      - jupyter-server==2.14.2
      - jupyter-server-terminals==0.5.3
      - jupyterlab==4.2.5
      - jupyterlab-pygments==0.3.0
      - jupyterlab-server==2.27.3
      - jupyterlab-widgets==3.0.13
      - kiwisolver==1.4.7
      - kornia==0.7.4
      - kornia-rs==0.1.7
      - lazy-loader==0.4
      - lightning-utilities==0.11.7
      - lvis==0.5.3
      - markdown-it-py==3.0.0
      - markupsafe==2.1.5
      - matplotlib==3.9.2
      - matplotlib-inline==0.1.7
      - mdurl==0.1.2
      - mistune==3.0.2
      - mmengine==0.10.5
      - mmengine-lite==0.10.6
      - mpmath==1.3.0
      - multidict==6.1.0
      - nbclient==0.10.0
      - nbconvert==7.16.4
      - nbformat==5.10.4
      - nest-asyncio==1.6.0
      - networkx==3.3
      - notebook==7.2.2
      - notebook-shim==0.2.4
      - numpy==2.1.1
      - nvidia-cublas-cu12==12.1.3.1
      - nvidia-cuda-cupti-cu12==12.1.105
      - nvidia-cuda-nvrtc-cu12==12.1.105
      - nvidia-cuda-runtime-cu12==12.1.105
      - nvidia-cudnn-cu12==9.1.0.70
      - nvidia-cufft-cu12==11.0.2.54
      - nvidia-curand-cu12==10.3.2.106
      - nvidia-cusolver-cu12==11.4.5.107
      - nvidia-cusparse-cu12==12.1.0.106
      - nvidia-ml-py==12.535.161
      - nvidia-nccl-cu12==2.20.5
      - nvidia-nvjitlink-cu12==12.6.68
      - nvidia-nvtx-cu12==12.1.105
      - nvitop==1.3.2
      - omegaconf==2.3.0
      - opencv-python==4.10.0.84
      - overrides==7.7.0
      - packaging==24.1
      - pandas==2.2.3
      - pandocfilters==1.5.1
      - parso==0.8.4
      - pexpect==4.9.0
      - pillow==10.4.0
      - platformdirs==4.3.6
      - portalocker==2.10.1
      - pot==0.9.5
      - prometheus-client==0.20.0
      - prompt-toolkit==3.0.47
      - propcache==0.2.0
      - protobuf==5.28.2
      - psutil==5.9.8
      - ptyprocess==0.7.0
      - pure-eval==0.2.3
      - pycocotools==2.0.8
      - pycparser==2.22
      - pygments==2.18.0
      - pyparsing==3.1.4
      - python-dateutil==2.9.0.post0
      - python-json-logger==2.0.7
      - pytorch-lightning==2.1.0
      - pytz==2024.2
      - pyyaml==6.0.2
      - pyzmq==26.2.0
      - referencing==0.35.1
      - requests==2.32.3
      - rfc3339-validator==0.1.4
      - rfc3986-validator==0.1.1
      - rich==13.9.2
      - rpds-py==0.20.0
      - scikit-image==0.24.0
      - scikit-learn==1.5.2
      - scipy==1.14.1
      - seaborn==0.13.2
      - segment-anything==1.0
      - send2trash==1.8.3
      - sentry-sdk==2.14.0
      - setproctitle==1.3.3
      - six==1.16.0
      - smmap==5.0.1
      - sniffio==1.3.1
      - soupsieve==2.6
      - stack-data==0.6.3
      - submitit==1.5.2
      - sympy==1.13.3
      - tabulate==0.9.0
      - termcolor==2.4.0
      - terminado==0.18.1
      - threadpoolctl==3.5.0
      - tidecv==1.0.1
      - tifffile==2024.9.20
      - timm==0.4.12
      - tinycss2==1.3.0
      - tomli==2.0.2
      - torch==2.4.1
      - torchmetrics==1.4.3
      - torchvision==0.19.1
      - tornado==6.4.1
      - tqdm==4.66.5
      - traitlets==5.14.3
      - triton==3.0.0
      - types-python-dateutil==2.9.0.20240906
      - typeshed-client==2.7.0
      - typing-extensions==4.12.2
      - tzdata==2024.2
      - uri-template==1.3.0
      - urllib3==2.2.3
      - wandb==0.18.2
      - wcwidth==0.2.13
      - webcolors==24.8.0
      - webencodings==0.5.1
      - websocket-client==1.8.0
      - widgetsnbextension==4.0.13
      - yacs==0.1.8
      - yapf==0.40.2
      - yarl==1.15.2
      - zipp==3.20.2