#!/usr/bin/env python3
"""
准备番茄数据集用于No Time to Train推理
"""
import json
import os
import shutil
import random
from pathlib import Path

def prepare_tomato_dataset():
    """准备番茄数据集"""
    
    # 设置路径
    base_path = Path("../datasets/tomato")
    output_path = Path("data/tomato_dataset")
    
    # 创建输出目录
    output_path.mkdir(parents=True, exist_ok=True)
    (output_path / "images").mkdir(exist_ok=True)
    (output_path / "annotations").mkdir(exist_ok=True)
    
    # 读取训练注释
    with open(base_path / "annotations/train_annotations/train_annotations.json", 'r') as f:
        train_data = json.load(f)
    
    # 读取测试注释  
    with open(base_path / "annotations/test_annotations/test_annotations.json", 'r') as f:
        test_data = json.load(f)
    
    print(f"训练图像数量: {len(train_data['images'])}")
    print(f"测试图像数量: {len(test_data['images'])}")
    print(f"类别: {[cat['name'] for cat in train_data['categories']]}")
    
    # 从训练集中为每个类别选择1-2个参考图像
    # 排除background类别(id=0)
    target_categories = [cat for cat in train_data['categories'] if cat['id'] != 0]
    print(f"目标类别: {[cat['name'] for cat in target_categories]}")
    
    # 统计每个类别的注释
    category_annotations = {cat['id']: [] for cat in target_categories}
    
    for ann in train_data['annotations']:
        if ann['category_id'] in category_annotations:
            category_annotations[ann['category_id']].append(ann)
    
    # 为每个类别选择参考图像
    reference_images = []
    reference_annotations = []
    used_image_ids = set()
    
    for cat in target_categories:
        cat_id = cat['id']
        cat_anns = category_annotations[cat_id]
        
        if not cat_anns:
            print(f"警告: 类别 {cat['name']} 没有注释")
            continue
            
        # 按图像ID分组
        image_anns = {}
        for ann in cat_anns:
            img_id = ann['image_id']
            if img_id not in image_anns:
                image_anns[img_id] = []
            image_anns[img_id].append(ann)
        
        # 选择有最多该类别实例的图像作为参考
        best_img_id = max(image_anns.keys(), key=lambda x: len(image_anns[x]))
        
        if best_img_id not in used_image_ids:
            used_image_ids.add(best_img_id)

            # 找到对应的图像信息
            img_info = next(img for img in train_data['images'] if img['id'] == best_img_id)
            reference_images.append(img_info)

            # 只选择该类别的第一个实例作为参考
            selected_ann = image_anns[best_img_id][0]
            reference_annotations.append(selected_ann)

            print(f"选择类别 {cat['name']} 的参考图像: {img_info['file_name']} (选择1个实例，原有 {len(image_anns[best_img_id])} 个实例)")
    
    # 创建参考数据集JSON
    reference_data = {
        "images": reference_images,
        "annotations": reference_annotations,
        "categories": target_categories
    }
    
    # 保存参考数据集
    with open(output_path / "annotations/tomato_references.json", 'w') as f:
        json.dump(reference_data, f, indent=2)
    
    # 创建目标数据集JSON (使用测试集)
    target_data = {
        "images": test_data['images'],
        "annotations": test_data['annotations'],
        "categories": target_categories
    }
    
    # 保存目标数据集
    with open(output_path / "annotations/tomato_targets.json", 'w') as f:
        json.dump(target_data, f, indent=2)
    
    # 复制图像文件
    print("复制参考图像...")
    for img in reference_images:
        src = base_path / "train_images" / img['file_name']
        dst = output_path / "images" / img['file_name']
        if src.exists():
            shutil.copy2(src, dst)
        else:
            print(f"警告: 找不到图像文件 {src}")
    
    print("复制目标图像...")
    for img in test_data['images']:
        src = base_path / "test_images" / img['file_name']
        dst = output_path / "images" / img['file_name']
        if src.exists():
            shutil.copy2(src, dst)
        else:
            print(f"警告: 找不到图像文件 {src}")
    
    print(f"数据集准备完成!")
    print(f"参考图像数量: {len(reference_images)}")
    print(f"目标图像数量: {len(test_data['images'])}")
    print(f"输出路径: {output_path}")
    
    return output_path, len(reference_images), len(target_categories)

if __name__ == "__main__":
    prepare_tomato_dataset()
