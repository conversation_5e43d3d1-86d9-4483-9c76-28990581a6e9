seed_everything: 42

model:
  class_path: no_time_to_train.pl_wrapper.sam2matcher_pl.Sam2MatcherLightningModel
  init_args:
    model_cfg:
      name: "matching_baseline_noAMG"
      sam2_cfg_file: "sam2_hiera_l.yaml"
      sam2_ckpt_path: "./checkpoints/sam2_hiera_large.pt"
      sam2_infer_cfgs:
        points_per_side: 32
        testing_point_bs: 256
        iou_thr: 0.4
        nms_thr: 0.5
        num_out_instance: 100
        kmeans_k: 4
        n_pca_components: 3
        cls_num_per_mask: 1
      encoder_cfg:
        name: "dinov2_large"
        img_size: 518
        patch_size: 14
      encoder_ckpt_path: "./checkpoints/dinov2/dinov2_vitl14_pretrain.pth"
      memory_bank_cfg:
        enable: True
        category_num: 3  # ripe, unripe, stem (excluding background)
        length: 1  # 1-shot per category
      dataset_name: tomato_dataset
      test:
        imgs_path: "./data/tomato_dataset/images"
        online_vis: True
        vis_thr: 0.3
    dataset_cfgs:
      fill_memory:
        name: "coco"
        root: "./data/tomato_dataset/images"
        json_file: "./data/tomato_dataset/annotations/tomato_references.json"
        memory_pkl: "./data/tomato_dataset/annotations/tomato_references.pkl"
        memory_length: 1
        cat_names: "ripe,unripe,stem"
        image_size: 518
        context_ratio: 0.2
        norm_img: False
      test:
        name: "coco"
        root: "./data/tomato_dataset/images"
        json_file: "./data/tomato_dataset/annotations/tomato_targets.json"
        cat_names: "ripe,unripe,stem"
        image_size: 1024
        norm_img: False
        with_query_points: False
    data_load_cfgs:
      workers: 2

trainer:
  devices: 1
  benchmark: true
  precision: 32
  callbacks:
    - class_path: pytorch_lightning.callbacks.TQDMProgressBar
      init_args:
        refresh_rate: 10
  logger:
    - class_path: pytorch_lightning.loggers.CSVLogger
      init_args:
        flush_logs_every_n_steps: 50
        save_dir: ./work_dirs/tomato_results
